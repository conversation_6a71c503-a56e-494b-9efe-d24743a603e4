"""
Local proxy server that handles authentication for DataImpulse proxy
This allows Chrome to connect to a local proxy without authentication
"""

import socket
import threading
import requests
import time
import logging
from urllib.parse import urlparse

class LocalProxyServer:
    """Local proxy server that forwards requests to authenticated proxy"""
    
    def __init__(self, local_port=8888):
        self.local_port = local_port
        self.running = False
        self.server_socket = None
        self.proxy_url = "http://e98f5489956302bda457__cr.fr:<EMAIL>:823"
        self.proxy_dict = {
            'http': self.proxy_url,
            'https': self.proxy_url
        }
        self.logger = logging.getLogger("LocalProxyServer")
    
    def start(self):
        """Start the local proxy server"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.local_port))
            self.server_socket.listen(5)
            
            self.running = True
            self.logger.info(f"Local proxy server started on 127.0.0.1:{self.local_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        self.logger.error(f"Error accepting connection: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to start proxy server: {e}")
    
    def stop(self):
        """Stop the local proxy server"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        self.logger.info("Local proxy server stopped")
    
    def handle_client(self, client_socket):
        """Handle client connection"""
        try:
            # Read the request
            request = client_socket.recv(4096).decode('utf-8')
            
            if not request:
                return
            
            # Parse the request
            lines = request.split('\n')
            if not lines:
                return
            
            first_line = lines[0]
            method, url, version = first_line.split(' ', 2)
            
            if method == 'CONNECT':
                # Handle HTTPS CONNECT method
                self.handle_connect(client_socket, url)
            else:
                # Handle HTTP requests
                self.handle_http_request(client_socket, request, method, url)
                
        except Exception as e:
            self.logger.error(f"Error handling client: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
    
    def handle_connect(self, client_socket, url):
        """Handle HTTPS CONNECT requests"""
        try:
            # Send 200 Connection established
            response = "HTTP/1.1 200 Connection established\r\n\r\n"
            client_socket.send(response.encode())
            
            # For HTTPS, we need to tunnel the connection
            # This is a simplified implementation
            host, port = url.split(':')
            port = int(port)
            
            # Create connection to target through our authenticated proxy
            # For simplicity, we'll just close the connection
            # A full implementation would tunnel the HTTPS traffic
            
        except Exception as e:
            self.logger.error(f"Error handling CONNECT: {e}")
    
    def handle_http_request(self, client_socket, request, method, url):
        """Handle HTTP requests"""
        try:
            # Make request through authenticated proxy
            headers = {}
            lines = request.split('\n')
            
            for line in lines[1:]:
                if ':' in line:
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()
            
            # Remove proxy-specific headers
            headers.pop('Proxy-Connection', None)
            headers.pop('Proxy-Authorization', None)
            
            # Make request through authenticated proxy
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                proxies=self.proxy_dict,
                timeout=30,
                verify=False,
                stream=True
            )
            
            # Send response back to client
            response_line = f"HTTP/1.1 {response.status_code} {response.reason}\r\n"
            client_socket.send(response_line.encode())
            
            # Send headers
            for key, value in response.headers.items():
                header_line = f"{key}: {value}\r\n"
                client_socket.send(header_line.encode())
            
            client_socket.send(b"\r\n")
            
            # Send body
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    client_socket.send(chunk)
                    
        except Exception as e:
            self.logger.error(f"Error handling HTTP request: {e}")
            # Send error response
            error_response = "HTTP/1.1 500 Internal Server Error\r\n\r\n"
            try:
                client_socket.send(error_response.encode())
            except:
                pass

def start_local_proxy_server(port=8888):
    """Start local proxy server in background thread"""
    proxy_server = LocalProxyServer(port)
    
    server_thread = threading.Thread(target=proxy_server.start, daemon=True)
    server_thread.start()
    
    # Give server time to start
    time.sleep(1)
    
    return proxy_server

def test_local_proxy():
    """Test the local proxy server"""
    print("🧪 Testing Local Proxy Server...")
    
    # Start local proxy
    proxy_server = start_local_proxy_server(8888)
    
    try:
        # Test with requests through local proxy
        local_proxy = {
            'http': 'http://127.0.0.1:8888',
            'https': 'http://127.0.0.1:8888'
        }
        
        response = requests.get(
            "http://httpbin.org/ip",
            proxies=local_proxy,
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"✅ Local proxy working: {response.text}")
        else:
            print(f"❌ Local proxy failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Local proxy test failed: {e}")
    finally:
        proxy_server.stop()

if __name__ == "__main__":
    test_local_proxy()
