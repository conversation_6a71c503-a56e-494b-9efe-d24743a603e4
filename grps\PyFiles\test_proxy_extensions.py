"""
Test script to verify proxy extension functionality with the updated driver

This script tests both incognito and normal modes to verify proxy extensions work correctly.
"""

import json
import os
import sys
from time import sleep

def test_proxy_extension_support():
    """Test proxy extension support in different browser modes"""
    
    print("🧪 Testing Proxy Extension Support")
    print("=" * 50)
    
    # Test configurations
    test_configs = [
        {
            "name": "No Proxy (Incognito Mode)",
            "proxy": None,
            "expected_incognito": True,
            "description": "Should use incognito mode when no proxy is configured"
        },
        {
            "name": "HTTP Proxy (Normal Mode)",
            "proxy": "http://127.0.0.1:8080",
            "expected_incognito": False,
            "description": "Should use normal mode to allow proxy extensions"
        },
        {
            "name": "SOCKS5 Proxy (Normal Mode)",
            "proxy": "socks5://127.0.0.1:1080",
            "expected_incognito": False,
            "description": "Should use normal mode for SOCKS5 proxy bridge"
        }
    ]
    
    for i, config in enumerate(test_configs, 1):
        print(f"\n🔧 Test {i}: {config['name']}")
        print(f"📝 Description: {config['description']}")
        print(f"🌐 Proxy: {config['proxy']}")
        print(f"🕵️ Expected Incognito: {config['expected_incognito']}")
        
        # Create temporary settings
        settings = {
            "use_proxy": config['proxy'] is not None,
            "proxy": config['proxy']
        }
        
        settings_path = "json/test_settings.json"
        try:
            with open(settings_path, 'w') as f:
                json.dump(settings, f, indent=4)
            print(f"✅ Created test settings: {settings_path}")
        except Exception as e:
            print(f"❌ Error creating test settings: {e}")
            continue
        
        # Test the driver configuration logic
        try:
            # Import the driver class
            from updated_groups import EnhancedSeleniumBaseDriver
            
            # Create a test instance
            driver = EnhancedSeleniumBaseDriver("<EMAIL>")
            
            # Check proxy configuration
            proxy_config = driver._get_proxy_config()
            print(f"🔍 Detected proxy config: {proxy_config}")
            
            # Check incognito mode logic
            use_incognito = proxy_config is None or 'proxy' not in proxy_config
            print(f"🕵️ Will use incognito: {use_incognito}")
            
            if use_incognito == config['expected_incognito']:
                print("✅ Incognito mode logic is correct")
            else:
                print("❌ Incognito mode logic is incorrect")
            
            # Clean up
            driver.quit()
            
        except Exception as e:
            print(f"❌ Error testing driver: {e}")
        
        print("-" * 30)
    
    # Clean up test file
    try:
        if os.path.exists(settings_path):
            os.remove(settings_path)
        print(f"🧹 Cleaned up test settings file")
    except Exception as e:
        print(f"⚠️ Could not clean up test file: {e}")

def test_chrome_options():
    """Test Chrome options for proxy extension support"""
    
    print("\n🔧 Testing Chrome Options for Proxy Extensions")
    print("=" * 50)
    
    try:
        from updated_groups import EnhancedSeleniumBaseDriver
        
        # Create test driver instance
        driver = EnhancedSeleniumBaseDriver("<EMAIL>")
        
        # Get Chrome options
        chrome_options = driver._get_chrome_options()
        
        print(f"📋 Total Chrome options: {len(chrome_options)}")
        
        # Check for extension-related options
        extension_options = [opt for opt in chrome_options if 'extension' in opt.lower()]
        print(f"🔌 Extension-related options: {len(extension_options)}")
        
        for opt in extension_options:
            print(f"   • {opt}")
        
        # Check for proxy-related options
        proxy_options = [opt for opt in chrome_options if any(keyword in opt.lower() 
                        for keyword in ['proxy', 'security', 'certificate', 'ssl'])]
        print(f"🌐 Proxy/Security-related options: {len(proxy_options)}")
        
        for opt in proxy_options[:10]:  # Show first 10
            print(f"   • {opt}")
        
        if len(proxy_options) > 10:
            print(f"   ... and {len(proxy_options) - 10} more")
        
        # Clean up
        driver.quit()
        
    except Exception as e:
        print(f"❌ Error testing Chrome options: {e}")

def create_proxy_test_settings():
    """Create test settings files for different proxy configurations"""
    
    print("\n📁 Creating Test Settings Files")
    print("=" * 50)
    
    test_settings = {
        "no_proxy": {
            "use_proxy": False,
            "proxy": None
        },
        "http_proxy": {
            "use_proxy": True,
            "proxy": "http://127.0.0.1:8080"
        },
        "socks5_proxy": {
            "use_proxy": True,
            "proxy": "socks5://127.0.0.1:1080"
        },
        "authenticated_http": {
            "use_proxy": True,
            "proxy": "http://user:<EMAIL>:8080"
        }
    }
    
    for name, settings in test_settings.items():
        filename = f"json/test_{name}_settings.json"
        try:
            with open(filename, 'w') as f:
                json.dump(settings, f, indent=4)
            print(f"✅ Created {filename}")
        except Exception as e:
            print(f"❌ Error creating {filename}: {e}")
    
    print("\n📝 To test different configurations:")
    print("   1. Copy one of the test_*_settings.json files to settings.json")
    print("   2. Run your main script")
    print("   3. Check the browser behavior (incognito vs normal mode)")

if __name__ == "__main__":
    print("🌐 Proxy Extension Support Test Suite")
    print("=" * 60)
    
    # Run tests
    test_proxy_extension_support()
    test_chrome_options()
    create_proxy_test_settings()
    
    print("\n🎯 Summary:")
    print("✅ The updated driver now:")
    print("   • Uses normal mode when proxy is configured (allows extensions)")
    print("   • Uses incognito mode when no proxy is configured")
    print("   • Enables Chrome extensions for proxy functionality")
    print("   • Includes --enable-extensions-in-incognito flag")
    print("   • Removes extension-blocking flags when proxy is used")
    
    print("\n💡 If proxy extensions still don't work:")
    print("   1. Check if the proxy extension is properly installed")
    print("   2. Verify the extension has necessary permissions")
    print("   3. Try manually enabling the extension in incognito mode")
    print("   4. Check Chrome's extension management page (chrome://extensions/)")
