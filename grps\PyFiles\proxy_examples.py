"""
Examples of how to use the updated_groups.py with non-authenticated proxies

This file demonstrates different proxy configurations that work with the modified code.
"""

import json
import os

# Example proxy configurations
PROXY_EXAMPLES = {
    "http_no_auth": {
        "description": "HTTP proxy without authentication",
        "proxy": "http://127.0.0.1:8080",
        "use_case": "Local proxy server or public proxy"
    },
    
    "http_with_auth": {
        "description": "HTTP proxy with authentication",
        "proxy": "http://username:<EMAIL>:8080",
        "use_case": "Private proxy service with credentials"
    },
    
    "socks5_no_auth": {
        "description": "SOCKS5 proxy without authentication",
        "proxy": "socks5://127.0.0.1:1080",
        "use_case": "Local SOCKS5 proxy or public SOCKS5 proxy"
    },
    
    "socks5_with_auth": {
        "description": "SOCKS5 proxy with authentication",
        "proxy": "socks5://username:<EMAIL>:1080",
        "use_case": "Private SOCKS5 proxy service with credentials"
    },
    
    "no_proxy": {
        "description": "Direct connection without proxy",
        "proxy": None,
        "use_case": "Direct internet connection"
    }
}

def create_settings_file(proxy_type="http_no_auth"):
    """
    Create a settings.json file with the specified proxy configuration
    
    Args:
        proxy_type (str): Type of proxy from PROXY_EXAMPLES keys
    """
    if proxy_type not in PROXY_EXAMPLES:
        print(f"❌ Unknown proxy type: {proxy_type}")
        print(f"Available types: {list(PROXY_EXAMPLES.keys())}")
        return False
    
    proxy_config = PROXY_EXAMPLES[proxy_type]
    
    # Create settings configuration
    settings = {
        "use_proxy": proxy_config["proxy"] is not None,
        "proxy": proxy_config["proxy"]
    }
    
    # Write to settings file
    settings_path = "json/settings.json"
    try:
        with open(settings_path, 'w') as f:
            json.dump(settings, f, indent=4)
        
        print(f"✅ Created settings.json with {proxy_config['description']}")
        print(f"📝 Proxy: {proxy_config['proxy']}")
        print(f"🎯 Use case: {proxy_config['use_case']}")
        return True
        
    except Exception as e:
        print(f"❌ Error creating settings file: {e}")
        return False

def create_profile_with_proxy(email, proxy_type="http_no_auth"):
    """
    Example of how to create a profile with specific proxy configuration
    
    Args:
        email (str): Email for the profile
        proxy_type (str): Type of proxy from PROXY_EXAMPLES keys
    """
    if proxy_type not in PROXY_EXAMPLES:
        print(f"❌ Unknown proxy type: {proxy_type}")
        return None
    
    proxy_config = PROXY_EXAMPLES[proxy_type]
    
    # Create profile configuration
    profile_proxy_config = {
        "proxy": proxy_config["proxy"],
        "proxy_type": proxy_type,
        "description": proxy_config["description"]
    } if proxy_config["proxy"] else None
    
    print(f"📋 Profile configuration for {email}:")
    print(f"   Proxy: {proxy_config['proxy']}")
    print(f"   Description: {proxy_config['description']}")
    
    return profile_proxy_config

def test_proxy_configurations():
    """Test different proxy configurations"""
    print("🧪 Testing different proxy configurations...\n")
    
    for proxy_type, config in PROXY_EXAMPLES.items():
        print(f"🔧 {config['description']}:")
        print(f"   Proxy URL: {config['proxy']}")
        print(f"   Use case: {config['use_case']}")
        
        # Show how authentication is detected
        if config['proxy']:
            has_auth = '@' in config['proxy']
            is_socks5 = config['proxy'].startswith('socks5://')
            print(f"   Has authentication: {has_auth}")
            print(f"   Is SOCKS5: {is_socks5}")
            
            if has_auth:
                print("   ➡️ Will use proxy bridge for authentication")
            else:
                print("   ➡️ Will use direct proxy connection")
        else:
            print("   ➡️ Direct connection (no proxy)")
        
        print()

if __name__ == "__main__":
    print("🌐 Proxy Configuration Examples for updated_groups.py\n")
    
    # Show all available configurations
    test_proxy_configurations()
    
    # Create example settings file
    print("📁 Creating example settings.json...")
    create_settings_file("http_no_auth")
    
    print("\n📝 To use different proxy types:")
    print("   create_settings_file('http_no_auth')     # HTTP proxy without auth")
    print("   create_settings_file('http_with_auth')   # HTTP proxy with auth")
    print("   create_settings_file('socks5_no_auth')   # SOCKS5 proxy without auth")
    print("   create_settings_file('socks5_with_auth') # SOCKS5 proxy with auth")
    print("   create_settings_file('no_proxy')         # No proxy (direct connection)")
    
    print("\n🔧 The updated_groups.py now supports:")
    print("   ✅ HTTP proxies with and without authentication")
    print("   ✅ SOCKS5 proxies with and without authentication")
    print("   ✅ Automatic detection of proxy type and authentication")
    print("   ✅ Proxy bridges for authenticated proxies")
    print("   ✅ Direct proxy connections for non-authenticated proxies")
