
import socket
import threading
import time
import requests
import ssl
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
import urllib.parse

class EnhancedProxyBridge:
    """Enhanced proxy bridge with better HTTPS support"""
    
    def __init__(self, remote_proxy_url, local_port=8085):
        self.remote_proxy_url = remote_proxy_url
        self.local_port = local_port
        self.local_proxy_url = f"http://127.0.0.1:{local_port}"
        self.server = None
        self.server_thread = None
        self.is_running = False
        
    def start(self):
        """Start the enhanced proxy bridge"""
        if self.is_running:
            return True
            
        try:
            # Create session with SSL verification disabled
            self.session = requests.Session()
            self.session.verify = False
            self.session.proxies = {
                'http': self.remote_proxy_url,
                'https': self.remote_proxy_url
            }
            
            # Disable SSL warnings
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
                daemon_threads = True
                allow_reuse_address = True
            
            class EnhancedProxyHandler(BaseHTTPRequestHandler):
                def __init__(self, session, *args, **kwargs):
                    self.session = session
                    super().__init__(*args, **kwargs)
                
                def do_CONNECT(self):
                    """Handle HTTPS CONNECT method"""
                    self.send_response(200, 'Connection established')
                    self.end_headers()
                
                def do_GET(self):
                    self._handle_request()
                
                def do_POST(self):
                    self._handle_request()
                
                def _handle_request(self):
                    try:
                        url = self.path
                        if not url.startswith('http'):
                            url = f"http://{self.headers.get('Host', 'localhost')}{url}"
                        
                        headers = {}
                        for name, value in self.headers.items():
                            if name.lower() not in ['connection', 'proxy-connection']:
                                headers[name] = value
                        
                        content_length = int(self.headers.get('Content-Length', 0))
                        body = self.rfile.read(content_length) if content_length > 0 else None
                        
                        response = self.session.request(
                            method=self.command,
                            url=url,
                            headers=headers,
                            data=body,
                            timeout=60,  # Increased timeout
                            stream=True,
                            verify=False  # Disable SSL verification
                        )
                        
                        self.send_response(response.status_code)
                        for name, value in response.headers.items():
                            if name.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(name, value)
                        self.end_headers()
                        
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                self.wfile.write(chunk)
                                
                    except Exception as e:
                        try:
                            self.send_error(500, f"Proxy error: {str(e)}")
                        except:
                            pass
                
                def log_message(self, format, *args):
                    pass
            
            def handler_factory(*args, **kwargs):
                return EnhancedProxyHandler(self.session, *args, **kwargs)
            
            self.server = ThreadingHTTPServer(('127.0.0.1', self.local_port), handler_factory)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            time.sleep(1)
            self.is_running = True
            return True
                
        except Exception as e:
            print(f"Failed to start enhanced proxy bridge: {str(e)}")
            return False
    
    def stop(self):
        if self.server and self.is_running:
            try:
                self.server.shutdown()
                self.server.server_close()
                if self.server_thread:
                    self.server_thread.join(timeout=5)
                self.is_running = False
            except Exception as e:
                print(f"Error stopping enhanced proxy bridge: {str(e)}")
    
    def get_local_proxy_url(self):
        if self.is_running:
            return self.local_proxy_url
        return None
