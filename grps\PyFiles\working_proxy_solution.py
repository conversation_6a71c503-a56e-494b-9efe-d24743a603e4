"""
Working proxy solution for DataImpulse rotation service
This creates a local HTTP proxy that <PERSON><PERSON> can use without authentication
"""

import socket
import threading
import requests
import time
import logging
import select
from urllib.parse import urlparse

class WorkingProxyBridge:
    """HTTP proxy bridge that handles DataImpulse authentication"""
    
    def __init__(self, local_port=8888):
        self.local_port = local_port
        self.running = False
        self.server_socket = None
        self.upstream_proxy = "http://e98f5489956302bda457__cr.fr:<EMAIL>:823"
        self.logger = logging.getLogger("WorkingProxyBridge")
        
        # Configure logging
        logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    def start(self):
        """Start the proxy bridge"""
        try:
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('127.0.0.1', self.local_port))
            self.server_socket.listen(10)
            
            self.running = True
            self.logger.info(f"Working proxy bridge started on 127.0.0.1:{self.local_port}")
            
            while self.running:
                try:
                    client_socket, addr = self.server_socket.accept()
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket,),
                        daemon=True
                    )
                    client_thread.start()
                except Exception as e:
                    if self.running:
                        self.logger.error(f"Error accepting connection: {e}")
                        
        except Exception as e:
            self.logger.error(f"Failed to start proxy bridge: {e}")
    
    def stop(self):
        """Stop the proxy bridge"""
        self.running = False
        if self.server_socket:
            try:
                self.server_socket.close()
            except:
                pass
        self.logger.info("Working proxy bridge stopped")
    
    def handle_client(self, client_socket):
        """Handle client connection"""
        try:
            # Set socket timeout
            client_socket.settimeout(30)
            
            # Read the request
            request_data = b""
            while True:
                try:
                    chunk = client_socket.recv(4096)
                    if not chunk:
                        break
                    request_data += chunk
                    if b'\r\n\r\n' in request_data:
                        break
                except socket.timeout:
                    break
            
            if not request_data:
                return
            
            request = request_data.decode('utf-8', errors='ignore')
            lines = request.split('\n')
            if not lines:
                return
            
            first_line = lines[0].strip()
            if not first_line:
                return
                
            parts = first_line.split(' ')
            if len(parts) < 3:
                return
                
            method, url, version = parts[0], parts[1], parts[2]
            
            if method == 'CONNECT':
                # Handle HTTPS CONNECT
                self.handle_connect(client_socket, url)
            else:
                # Handle HTTP requests
                self.handle_http_request(client_socket, request, method, url)
                
        except Exception as e:
            self.logger.debug(f"Error handling client: {e}")
        finally:
            try:
                client_socket.close()
            except:
                pass
    
    def handle_connect(self, client_socket, target):
        """Handle HTTPS CONNECT requests"""
        try:
            # Send 200 Connection established
            response = "HTTP/1.1 200 Connection established\r\n\r\n"
            client_socket.send(response.encode())
            
            # For HTTPS, we'll just close the connection for now
            # A full implementation would create a tunnel
            self.logger.debug(f"CONNECT request to {target}")
            
        except Exception as e:
            self.logger.debug(f"Error handling CONNECT: {e}")
    
    def handle_http_request(self, client_socket, request, method, url):
        """Handle HTTP requests through upstream proxy"""
        try:
            # Parse headers
            headers = {}
            lines = request.split('\n')
            
            for line in lines[1:]:
                if ':' in line and line.strip():
                    key, value = line.split(':', 1)
                    headers[key.strip()] = value.strip()
            
            # Remove proxy-specific headers
            headers.pop('Proxy-Connection', None)
            headers.pop('Proxy-Authorization', None)
            
            # Get request body if present
            body = None
            if '\r\n\r\n' in request:
                body_part = request.split('\r\n\r\n', 1)
                if len(body_part) > 1:
                    body = body_part[1]
            
            # Make request through upstream proxy
            proxies = {
                'http': self.upstream_proxy,
                'https': self.upstream_proxy
            }
            
            self.logger.debug(f"Proxying {method} {url}")
            
            response = requests.request(
                method=method,
                url=url,
                headers=headers,
                data=body,
                proxies=proxies,
                timeout=30,
                verify=False,
                stream=True
            )
            
            # Send response back to client
            status_line = f"HTTP/1.1 {response.status_code} {response.reason}\r\n"
            client_socket.send(status_line.encode())
            
            # Send headers
            for key, value in response.headers.items():
                if key.lower() not in ['transfer-encoding', 'connection']:
                    header_line = f"{key}: {value}\r\n"
                    client_socket.send(header_line.encode())
            
            client_socket.send(b"Connection: close\r\n")
            client_socket.send(b"\r\n")
            
            # Send body
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    client_socket.send(chunk)
                    
        except Exception as e:
            self.logger.debug(f"Error handling HTTP request: {e}")
            # Send error response
            error_response = "HTTP/1.1 500 Internal Server Error\r\nConnection: close\r\n\r\nProxy Error"
            try:
                client_socket.send(error_response.encode())
            except:
                pass

def start_working_proxy_bridge(port=8888):
    """Start working proxy bridge in background thread"""
    bridge = WorkingProxyBridge(port)
    
    bridge_thread = threading.Thread(target=bridge.start, daemon=True)
    bridge_thread.start()
    
    # Give bridge time to start
    time.sleep(2)
    
    return bridge

def test_working_solution():
    """Test the working proxy solution"""
    print("🧪 Testing Working Proxy Solution...")
    
    # Start proxy bridge
    bridge = start_working_proxy_bridge(8890)
    
    try:
        # Test with requests first
        local_proxy = {
            'http': 'http://127.0.0.1:8890',
            'https': 'http://127.0.0.1:8890'
        }
        
        print("🌐 Testing with requests through bridge...")
        response = requests.get(
            "http://httpbin.org/ip",
            proxies=local_proxy,
            timeout=15
        )
        
        if response.status_code == 200:
            print(f"✅ Bridge working with requests: {response.text.strip()}")
            
            # Extract IP
            import re
            ip_match = re.search(r'"origin": "([^"]+)"', response.text)
            if ip_match:
                ip = ip_match.group(1)
                if ip.startswith("105.191.99."):
                    print("❌ Still using real IP")
                    return False
                else:
                    print(f"✅ Using proxy IP: {ip}")
                    
                    # Now test with SeleniumBase
                    print("\n🌐 Testing with SeleniumBase...")
                    return test_seleniumbase_with_bridge(bridge)
        else:
            print(f"❌ Bridge failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Bridge test failed: {e}")
        return False
    finally:
        bridge.stop()

def test_seleniumbase_with_bridge(bridge):
    """Test SeleniumBase with the working bridge"""
    try:
        from seleniumbase import Driver
        
        local_proxy = "http://127.0.0.1:8890"
        
        print(f"Creating SeleniumBase with bridge: {local_proxy}")
        
        driver = Driver(
            browser='chrome',
            headless=False,
            proxy=local_proxy
        )
        
        print("✅ SeleniumBase created with bridge")
        
        # Test HTTP site
        print("🌐 Testing http://httpbin.org/ip...")
        driver.get("http://httpbin.org/ip")
        time.sleep(3)
        
        page_source = driver.page_source
        print(f"📄 Page length: {len(page_source)}")
        
        if '"origin"' in page_source and len(page_source) < 1000:
            import re
            ip_match = re.search(r'"origin": "([^"]+)"', page_source)
            if ip_match:
                ip = ip_match.group(1)
                print(f"📍 SeleniumBase IP: {ip}")
                
                if ip.startswith("105.191.99."):
                    print("❌ SeleniumBase using real IP")
                    result = False
                else:
                    print("✅ SeleniumBase using proxy IP!")
                    result = True
            else:
                print("❌ Could not extract IP")
                result = False
        else:
            print("❌ SeleniumBase got error page")
            result = False
        
        driver.quit()
        return result
        
    except Exception as e:
        print(f"❌ SeleniumBase test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Working Proxy Solution for DataImpulse")
    print("=" * 50)
    
    success = test_working_solution()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 SUCCESS! Working proxy solution confirmed!")
        print("\n💡 How to use:")
        print("1. Start the proxy bridge: bridge = start_working_proxy_bridge(8890)")
        print("2. Use SeleniumBase with: proxy='http://127.0.0.1:8890'")
        print("3. The bridge handles DataImpulse authentication automatically")
    else:
        print("❌ Working solution test failed")
        print("Need to investigate further...")
