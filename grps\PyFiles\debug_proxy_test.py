"""
Debug proxy test to see what IP is being returned
"""

import os
import sys
import time
import logging

# Add current directory to path for imports
sys.path.append(os.path.dirname(__file__))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def debug_proxy_test():
    """Debug test to see actual IP being used"""
    print("🔍 Debug Proxy Test...")
    
    try:
        from updated_groups import EnhancedSeleniumBaseDriver
        
        # Create enhanced driver instance
        print("Creating Enhanced SeleniumBase Driver...")
        driver = EnhancedSeleniumBaseDriver("<EMAIL>", "password", "test-agent", 1)
        
        print("✅ Driver created successfully")
        
        # Navigate to IP checking service
        print("\n🌐 Navigating to https://api.myip.com...")
        driver.go("https://api.myip.com")
        time.sleep(5)
        
        # Get page content and print it
        page_source = driver.browser.page_source
        print("\n📄 Page Source:")
        print("=" * 50)
        print(page_source)
        print("=" * 50)
        
        # Try to extract IP
        import re
        ip_patterns = [
            r'"ip":"([^"]+)"',
            r'"origin":"([^"]+)"',
            r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})'
        ]
        
        for pattern in ip_patterns:
            matches = re.findall(pattern, page_source)
            if matches:
                print(f"\n🔍 Found IPs with pattern '{pattern}': {matches}")
        
        # Also check current URL
        current_url = driver.browser.current_url
        print(f"\n🌐 Current URL: {current_url}")
        
        # Clean up
        print("\n🧹 Cleaning up...")
        driver.browser.quit()
        print("✅ Browser closed successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_seleniumbase_proxy_directly():
    """Test SeleniumBase proxy configuration directly"""
    print("\n🔍 Testing SeleniumBase Proxy Directly...")
    
    try:
        from seleniumbase import Driver as SBDriver
        
        proxy_string = "http://e98f5489956302bda457__cr.fr:<EMAIL>:823"
        
        print(f"Creating SeleniumBase driver with proxy: {proxy_string.split('@')[0]}@***")
        
        # Create driver with proxy
        driver = SBDriver(
            browser='chrome',
            headless=False,
            proxy=proxy_string
        )
        
        print("✅ SeleniumBase driver created")
        
        # Navigate to IP service
        print("🌐 Navigating to https://api.myip.com...")
        driver.get("https://api.myip.com")
        time.sleep(5)
        
        # Get page source
        page_source = driver.page_source
        print("\n📄 Page Source:")
        print("=" * 50)
        print(page_source)
        print("=" * 50)
        
        # Extract IP
        import re
        ip_match = re.search(r'"ip":"([^"]+)"', page_source)
        if ip_match:
            ip = ip_match.group(1)
            print(f"\n📍 Detected IP: {ip}")
            
            if ip.startswith("169.155.251."):
                print("✅ SUCCESS: Using DataImpulse proxy IP!")
            elif ip.startswith("105.191.99."):
                print("❌ ISSUE: Using real IP (Morocco)")
            else:
                print(f"🔍 Unknown IP: {ip}")
        else:
            print("❌ Could not extract IP from response")
        
        # Clean up
        driver.quit()
        print("✅ SeleniumBase driver closed")
        
        return True
        
    except Exception as e:
        print(f"❌ SeleniumBase direct test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run debug tests"""
    print("🚀 Debug Proxy Tests")
    print("=" * 60)
    
    # Test SeleniumBase directly
    direct_test = test_seleniumbase_proxy_directly()
    
    # Test with our enhanced driver
    enhanced_test = debug_proxy_test()
    
    print("\n" + "=" * 60)
    print("📋 Debug Results:")
    print(f"  SeleniumBase Direct: {'✅ PASSED' if direct_test else '❌ FAILED'}")
    print(f"  Enhanced Driver: {'✅ PASSED' if enhanced_test else '❌ FAILED'}")

if __name__ == "__main__":
    main()
