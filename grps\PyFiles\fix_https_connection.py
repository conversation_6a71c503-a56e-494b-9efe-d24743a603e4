#!/usr/bin/env python3
"""
Fix HTTPS connection issues with proxy bridge
This script diagnoses and fixes HTTPS connection problems
"""

import os
import sys
import json
import logging
import requests
import time

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_https_connections():
    """Test HTTPS connections through proxy bridge"""
    
    logger.info("🔍 Testing HTTPS connections through proxy bridge")
    
    # Read proxy configuration
    current_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(current_dir, "json", "settings.json")
    
    if not os.path.exists(settings_path):
        logger.error("❌ settings.json not found")
        return False
    
    try:
        with open(settings_path, 'r') as f:
            settings_data = json.load(f)
        
        remote_proxy = settings_data.get('proxy')
        if not remote_proxy:
            logger.error("❌ No proxy URL found")
            return False
        
        logger.info(f"📡 Remote proxy: {remote_proxy.split('@')[0]}@***")
        
        # Test different types of connections
        test_sites = [
            ("http://httpbin.org/ip", "HTTP - Basic"),
            ("https://httpbin.org/ip", "HTTPS - Basic"),
            ("https://api.ipify.org?format=json", "HTTPS - API"),
            ("https://www.google.com", "HTTPS - Google"),
            ("https://www.browserscan.net", "HTTPS - Bot Detection"),
            ("https://accounts.google.com", "HTTPS - Google Accounts"),
        ]
        
        # Test direct proxy connection
        logger.info("\n🧪 Testing direct proxy connection:")
        test_direct_proxy_https(remote_proxy, test_sites)
        
        # Test proxy bridge connection
        logger.info("\n🧪 Testing proxy bridge connection:")
        test_proxy_bridge_https(remote_proxy, test_sites)
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Test failed with error: {str(e)}")
        return False

def test_direct_proxy_https(proxy_url, test_sites):
    """Test direct HTTPS connections through proxy"""
    
    proxies = {
        'http': proxy_url,
        'https': proxy_url
    }
    
    for url, description in test_sites:
        logger.info(f"🔗 Testing {description}: {url}")
        try:
            # Use verify=False to bypass SSL verification issues
            response = requests.get(url, proxies=proxies, timeout=30, verify=False)
            if response.status_code == 200:
                logger.info(f"✅ {description}: SUCCESS ({response.status_code})")
            else:
                logger.warning(f"⚠️ {description}: HTTP {response.status_code}")
        except requests.exceptions.SSLError as e:
            logger.error(f"❌ {description}: SSL_ERROR - {str(e)}")
        except requests.exceptions.Timeout:
            logger.error(f"❌ {description}: TIMEOUT")
        except requests.exceptions.ConnectionError as e:
            logger.error(f"❌ {description}: CONNECTION_ERROR - {str(e)}")
        except Exception as e:
            logger.error(f"❌ {description}: ERROR - {str(e)}")
        
        time.sleep(2)

def test_proxy_bridge_https(proxy_url, test_sites):
    """Test HTTPS connections through proxy bridge"""
    
    try:
        # Import and start enhanced proxy bridge
        from simple_http_proxy_bridge import SimpleProxyBridge
        
        bridge = SimpleProxyBridge(proxy_url, local_port=8084)
        
        if not bridge.start():
            logger.error("❌ Failed to start proxy bridge")
            return False
        
        local_proxy_url = bridge.get_local_proxy_url()
        logger.info(f"🌉 Proxy bridge started: {local_proxy_url}")
        
        proxies = {
            'http': local_proxy_url,
            'https': local_proxy_url
        }
        
        for url, description in test_sites:
            logger.info(f"🔗 Testing {description}: {url}")
            try:
                # Use verify=False to bypass SSL verification issues
                response = requests.get(url, proxies=proxies, timeout=30, verify=False)
                if response.status_code == 200:
                    logger.info(f"✅ {description}: SUCCESS ({response.status_code})")
                else:
                    logger.warning(f"⚠️ {description}: HTTP {response.status_code}")
            except requests.exceptions.SSLError as e:
                logger.error(f"❌ {description}: SSL_ERROR - {str(e)}")
            except requests.exceptions.Timeout:
                logger.error(f"❌ {description}: TIMEOUT")
            except requests.exceptions.ConnectionError as e:
                logger.error(f"❌ {description}: CONNECTION_ERROR - {str(e)}")
            except Exception as e:
                logger.error(f"❌ {description}: ERROR - {str(e)}")
            
            time.sleep(2)
        
        # Clean up
        bridge.stop()
        return True
        
    except Exception as e:
        logger.error(f"❌ Proxy bridge test failed: {str(e)}")
        return False

def fix_chrome_ssl_options():
    """Add Chrome options to fix SSL/HTTPS issues"""
    
    logger.info("🔧 Generating Chrome SSL fix options")
    
    ssl_fix_options = [
        # SSL and Certificate options
        '--ignore-certificate-errors',
        '--ignore-ssl-errors',
        '--ignore-certificate-errors-spki-list',
        '--ignore-certificate-errors-skip-list',
        '--disable-certificate-transparency',
        '--allow-insecure-localhost',
        '--disable-web-security',
        '--allow-running-insecure-content',
        '--reduce-security-for-testing',
        
        # Proxy-specific SSL options
        '--proxy-bypass-list=<-loopback>',
        '--host-resolver-rules=MAP * ~NOTFOUND , EXCLUDE localhost',
        '--disable-features=VizDisplayCompositor,TranslateUI',
        
        # Network and connection options
        '--disable-background-networking',
        '--disable-background-timer-throttling',
        '--disable-backgrounding-occluded-windows',
        '--disable-renderer-backgrounding',
        '--aggressive-cache-discard',
        '--disable-extensions-http-throttling',
        
        # Additional stability options
        '--no-sandbox',
        '--disable-dev-shm-usage',
        '--disable-gpu',
        '--remote-debugging-port=0',  # Disable remote debugging
        '--disable-logging',
        '--disable-gpu-logging',
        '--silent',
        '--log-level=3',
    ]
    
    logger.info("✅ Chrome SSL fix options generated")
    logger.info("💡 Add these options to your Chrome configuration:")
    
    for option in ssl_fix_options:
        print(f"   '{option}',")
    
    return ssl_fix_options

def create_enhanced_proxy_bridge():
    """Create an enhanced proxy bridge with HTTPS support"""
    
    logger.info("🔧 Creating enhanced proxy bridge with HTTPS support")
    
    enhanced_bridge_code = '''
import socket
import threading
import time
import requests
import ssl
from http.server import HTTPServer, BaseHTTPRequestHandler
from socketserver import ThreadingMixIn
import urllib.parse

class EnhancedProxyBridge:
    """Enhanced proxy bridge with better HTTPS support"""
    
    def __init__(self, remote_proxy_url, local_port=8085):
        self.remote_proxy_url = remote_proxy_url
        self.local_port = local_port
        self.local_proxy_url = f"http://127.0.0.1:{local_port}"
        self.server = None
        self.server_thread = None
        self.is_running = False
        
    def start(self):
        """Start the enhanced proxy bridge"""
        if self.is_running:
            return True
            
        try:
            # Create session with SSL verification disabled
            self.session = requests.Session()
            self.session.verify = False
            self.session.proxies = {
                'http': self.remote_proxy_url,
                'https': self.remote_proxy_url
            }
            
            # Disable SSL warnings
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
            
            class ThreadingHTTPServer(ThreadingMixIn, HTTPServer):
                daemon_threads = True
                allow_reuse_address = True
            
            class EnhancedProxyHandler(BaseHTTPRequestHandler):
                def __init__(self, session, *args, **kwargs):
                    self.session = session
                    super().__init__(*args, **kwargs)
                
                def do_CONNECT(self):
                    """Handle HTTPS CONNECT method"""
                    self.send_response(200, 'Connection established')
                    self.end_headers()
                
                def do_GET(self):
                    self._handle_request()
                
                def do_POST(self):
                    self._handle_request()
                
                def _handle_request(self):
                    try:
                        url = self.path
                        if not url.startswith('http'):
                            url = f"http://{self.headers.get('Host', 'localhost')}{url}"
                        
                        headers = {}
                        for name, value in self.headers.items():
                            if name.lower() not in ['connection', 'proxy-connection']:
                                headers[name] = value
                        
                        content_length = int(self.headers.get('Content-Length', 0))
                        body = self.rfile.read(content_length) if content_length > 0 else None
                        
                        response = self.session.request(
                            method=self.command,
                            url=url,
                            headers=headers,
                            data=body,
                            timeout=60,  # Increased timeout
                            stream=True,
                            verify=False  # Disable SSL verification
                        )
                        
                        self.send_response(response.status_code)
                        for name, value in response.headers.items():
                            if name.lower() not in ['connection', 'transfer-encoding']:
                                self.send_header(name, value)
                        self.end_headers()
                        
                        for chunk in response.iter_content(chunk_size=8192):
                            if chunk:
                                self.wfile.write(chunk)
                                
                    except Exception as e:
                        try:
                            self.send_error(500, f"Proxy error: {str(e)}")
                        except:
                            pass
                
                def log_message(self, format, *args):
                    pass
            
            def handler_factory(*args, **kwargs):
                return EnhancedProxyHandler(self.session, *args, **kwargs)
            
            self.server = ThreadingHTTPServer(('127.0.0.1', self.local_port), handler_factory)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            time.sleep(1)
            self.is_running = True
            return True
                
        except Exception as e:
            print(f"Failed to start enhanced proxy bridge: {str(e)}")
            return False
    
    def stop(self):
        if self.server and self.is_running:
            try:
                self.server.shutdown()
                self.server.server_close()
                if self.server_thread:
                    self.server_thread.join(timeout=5)
                self.is_running = False
            except Exception as e:
                print(f"Error stopping enhanced proxy bridge: {str(e)}")
    
    def get_local_proxy_url(self):
        if self.is_running:
            return self.local_proxy_url
        return None
'''
    
    # Save the enhanced bridge code
    with open('enhanced_proxy_bridge.py', 'w') as f:
        f.write(enhanced_bridge_code)
    
    logger.info("✅ Enhanced proxy bridge code saved to enhanced_proxy_bridge.py")
    return True

if __name__ == "__main__":
    print("=" * 70)
    print("🔧 HTTPS CONNECTION FIX DIAGNOSTIC")
    print("=" * 70)
    
    # Test HTTPS connections
    test_https_connections()
    
    print("\n" + "=" * 70)
    print("🔧 CHROME SSL FIX OPTIONS")
    print("=" * 70)
    
    # Generate Chrome SSL fix options
    fix_chrome_ssl_options()
    
    print("\n" + "=" * 70)
    print("🔧 ENHANCED PROXY BRIDGE")
    print("=" * 70)
    
    # Create enhanced proxy bridge
    create_enhanced_proxy_bridge()
    
    print("\n" + "=" * 70)
    print("💡 RECOMMENDATIONS:")
    print("   1. Add the SSL fix options to your Chrome configuration")
    print("   2. Use the enhanced proxy bridge for better HTTPS support")
    print("   3. Consider using HTTP sites for testing first")
    print("   4. Check if your proxy provider supports HTTPS properly")
    print("=" * 70)
